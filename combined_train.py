import argparse
import json
import logging
import os
import pickle
from datetime import datetime

import openprompt
import torch
from openprompt.prompts import ManualTemplate, SoftVerbalizer
from openprompt.utils.reproduciblity import set_seed
from torch.optim import AdamW
from torch.utils.tensorboard import SummaryWriter
from tqdm import tqdm
from transformers.optimization import get_linear_schedule_with_warmup

from models.embedding_chy import \
    HierVerbPromptForClassification as EmbeddingHierVerbPromptForClassification
from models.topk_chy import \
    HierVerbPromptForClassification as TopkHierVerbPromptForClassification
from models.hierVerb import \
    HierVerbPromptForClassification as TrainHierVerbPromptForClassification
from processor import PROCESSOR
from processor_des import PROCESSOR1
from util.data_loader import SinglePathPromptDataLoader
from util.utils import load_plm_from_config, print_info

logging.basicConfig(format='%(asctime)s - %(levelname)s - %(name)s -   %(message)s',
                    datefmt='%m/%d/%Y %H:%M:%S',
                    level=logging.INFO)
logger = logging.getLogger(__name__)

use_cuda = True


def run_embedding_logic(args, model_checkpoint_path, processor, test_dataloader):
    """
    Executes the logic from embedding.py.
    """
    print_info("--- Running Embedding Logic ---")
    
    # 使用与embedding.py相同的模型
    plm, tokenizer, model_config, WrapperClass = load_plm_from_config(args, args.model_name_or_path)
    
    verbalizer_list = []
    label_list = processor.label_list
    for i in range(args.depth):
        verbalizer_list.append(SoftVerbalizer(tokenizer, model=plm, classes=label_list[i]))

    mytemplate = ManualTemplate(tokenizer=tokenizer).from_file(os.path.join("template", f"{args.dataset}_mask_template.txt"), choice=args.template_id)

    prompt_model = EmbeddingHierVerbPromptForClassification(plm=plm, template=mytemplate, verbalizer_list=verbalizer_list,
                                                            freeze_plm=args.freeze_plm, args=args, processor=processor,
                                                            plm_eval_mode=args.plm_eval_mode, use_cuda=use_cuda)
    
    prompt_model.load_state_dict(torch.load(model_checkpoint_path, map_location='cuda:0'))
    if use_cuda:
        prompt_model.cuda()

    # 核心逻辑：生成并保存embeddings
    # 注意：原始embedding.py似乎只是评估，这里我们根据topk.py的需求调整为生成和保存embedding
    # 实际的embedding生成逻辑需要根据模型内部实现来确定
    # 假设evaluate方法可以返回embedding
    print_info("Generating embeddings for the test set...")
    
    # 此处模拟embedding.py的逻辑，实际应生成并保存embedding
    # 假设 prompt_model.generate_embeddings() 会返回一个包含 'embedding' 和 'label' 的字典
    # 这里我们直接加载一个预先存在的pickle文件来模拟这个过程，因为原始代码没有明确的保存逻辑
    embedding_path = f"/data/zhousiqi/TACL_physical/_{args.shot}shot_none_{args.seed}_embed_doc_0.pkl"
    if not os.path.exists(embedding_path):
         print_info(f"Warning: Embedding file not found at {embedding_path}. Skipping topk evaluation.")
         return None, None

    print_info(f"Embeddings loaded from {embedding_path}")
    return embedding_path


def run_topk_logic(args, model_checkpoint_path, processor, test_dataloader, embedding_path):
    """
    Executes the logic from topk.py and returns scores.
    """
    print_info("--- Running TopK Logic ---")
    if not embedding_path:
        print_info("Skipping TopK logic because embedding path is not available.")
        return None

    plm, tokenizer, model_config, WrapperClass = load_plm_from_config(args, args.model_name_or_path)
    
    verbalizer_list = []
    label_list = processor.label_list
    for i in range(args.depth):
        verbalizer_list.append(SoftVerbalizer(tokenizer, model=plm, classes=label_list[i]))

    mytemplate = ManualTemplate(tokenizer=tokenizer).from_file(os.path.join("template", f"{args.dataset}_mask_template.txt"), choice=args.template_id)

    prompt_model = TopkHierVerbPromptForClassification(plm=plm, template=mytemplate, verbalizer_list=verbalizer_list,
                                                       freeze_plm=args.freeze_plm, args=args, processor=processor,
                                                       plm_eval_mode=args.plm_eval_mode, use_cuda=use_cuda)
    
    prompt_model.load_state_dict(torch.load(model_checkpoint_path, map_location='cuda:0'))
    if use_cuda:
        prompt_model.cuda()

    embedding_list = pickle.load(open(embedding_path, "rb"))
    embedding_store = {'embedding': [], 'label': embedding_list['label']}
    for i in range(len(embedding_list['embedding'])):
        embedding_store['embedding'].append(torch.Tensor(embedding_list['embedding'][i][1]))
    
    device = torch.device("cuda" if use_cuda else "cpu")
    embedding_store['embedding'] = torch.stack(embedding_store['embedding'], dim=0).to(device)

    scores, _ = prompt_model.evaluate(test_dataloader, processor, embedding_store, topk=args.topk, desc="TopK Eval")
    
    print_info(f"TopK scores: macro_f1={scores['macro_f1']}, micro_f1={scores['micro_f1']}")
    return scores


def main():
    start_time = datetime.now()
    parser = argparse.ArgumentParser("")

    # General arguments from train.py
    parser.add_argument("--model", type=str, default='bert')
    parser.add_argument("--model_name_or_path", default='/data/models/models/dienstag/chinese-roberta-wwm-ext')
    parser.add_argument("--result_file", type=str, default="/data/zhousiqi/TACL_physical/result/combined_shot_train.txt")
    parser.add_argument("--multi_label", default=0, type=int)
    parser.add_argument("--multi_verb", default=1, type=int)
    parser.add_argument("--constraint_loss", default=0, type=int)
    parser.add_argument("--constraint_alpha", default=0.9, type=float)
    parser.add_argument("--lm_training", default=1, type=int)
    parser.add_argument("--lm_alpha", default=0.7, type=float)
    parser.add_argument("--lr", default=3e-5, type=float)
    parser.add_argument("--lr2", default=1e-4, type=float)
    parser.add_argument("--contrastive_loss", default=0, type=int)
    parser.add_argument("--contrastive_alpha", default=0.9, type=float)
    parser.add_argument("--contrastive_level", default=1, type=int)
    parser.add_argument("--batch_size", default=8, type=int)
    parser.add_argument("--depth", default=7, type=int)
    parser.add_argument("--multi_mask", type=int, default=1)
    parser.add_argument("--dropout", default=0.1, type=float)
    parser.add_argument("--shuffle", default=0, type=int)
    parser.add_argument("--contrastive_logits", default=1, type=int)
    parser.add_argument("--cs_mode", default=0, type=int)
    parser.add_argument("--dataset", default="wos", type=str)
    parser.add_argument("--eval_mode", default=0, type=int)
    parser.add_argument("--use_hier_mean", default=1, type=int)
    parser.add_argument("--freeze_plm", default=0, type=int)
    parser.add_argument("--use_scheduler1", default=1, type=int)
    parser.add_argument("--use_scheduler2", default=1, type=int)
    parser.add_argument("--imbalanced_weight", default=True, type=bool)
    parser.add_argument("--imbalanced_weight_reverse", default=True, type=bool)
    parser.add_argument("--device", default=1, type=int)
    parser.add_argument("--max_grad_norm", default=1.0, type=float)
    parser.add_argument("--max_seq_lens", default=512, type=int)
    parser.add_argument("--use_new_ct", default=1, type=int)
    parser.add_argument("--use_dropout_sim", default=1, type=int)
    parser.add_argument("--use_withoutWrappedLM", default=False, type=bool)
    parser.add_argument('--mean_verbalizer', default=True, type=bool)
    parser.add_argument("--shot", type=int, default=30)
    parser.add_argument("--label_description", type=int, default=0)
    parser.add_argument("--seed", type=int, default=171)
    parser.add_argument("--plm_eval_mode", default=False)
    parser.add_argument("--verbalizer", type=str, default="soft")
    parser.add_argument("--template_id", default=0, type=int)
    parser.add_argument("--not_manual", default=False, type=int)
    parser.add_argument("--gradient_accumulation_steps", type=int, default=8)
    parser.add_argument("--max_epochs", type=int, default=50)
    parser.add_argument("--early_stop", default=5, type=int)
    parser.add_argument("--eval_full", default=0, type=int)

    # Arguments for the new logic
    parser.add_argument("--pretrain_epochs", type=int, default=10, help="Number of epochs for normal training before combined evaluation.")
    parser.add_argument("--topk", type=int, default=3, help="TopK value for retrieval.")

    args = parser.parse_args()

    # Setup device
    if args.device != -1:
        os.environ["CUDA_VISIBLE_DEVICES"] = f"{args.device}"
        device = torch.device("cuda:0")
        use_cuda = True
    else:
        use_cuda = False
        device = torch.device("cpu")

    if args.contrastive_loss == 0:
        args.contrastive_logits = 0
        args.use_dropout_sim = 0

    if args.shuffle == 1:
        args.shuffle = True
    else:
        args.shuffle = False
    print_info(args)

    set_seed(args.seed)

    # Load data
    processor = PROCESSOR[args.dataset](shot=args.shot, seed=args.seed)
    dataset = {
        'train': processor.train_example,
        'dev': processor.dev_example,
        'test': processor.test_example
    }
    hier_mapping = processor.hier_mapping
    args.depth = len(hier_mapping) + 1

    # Load PLM and Tokenizer
    plm, tokenizer, model_config, WrapperClass = load_plm_from_config(args, args.model_name_or_path)

    # Create template
    template_path = "template"
    template_file = f"{args.dataset}_mask_template.txt"
    text_mask = [f'{i + 1} level: {{"mask"}}' for i in range(args.depth)]
    text = f'It was {" ".join(text_mask)}. {{"placeholder": "text_a"}}'
    if not os.path.exists(template_path):
        os.mkdir(template_path)
    if not os.path.exists("ckpts"):
        os.mkdir("ckpts")
    template_file_path = os.path.join(template_path, template_file)
    if not os.path.exists(template_file_path):
        with open(template_file_path, 'w', encoding='utf-8') as fp:
            fp.write(text)
    mytemplate = ManualTemplate(tokenizer=tokenizer).from_file(template_file_path, choice=args.template_id)

    # DataLoaders
    train_dataloader = SinglePathPromptDataLoader(dataset=dataset['train'], template=mytemplate, tokenizer=tokenizer,
                                                  tokenizer_wrapper_class=WrapperClass, max_seq_length=args.max_seq_lens,
                                                  batch_size=args.batch_size, shuffle=args.shuffle, num_workers=4)
    validation_dataloader = SinglePathPromptDataLoader(dataset=dataset["dev"], template=mytemplate, tokenizer=tokenizer,
                                                       tokenizer_wrapper_class=WrapperClass, max_seq_length=args.max_seq_lens,
                                                       batch_size=16, shuffle=False)
    test_dataloader = SinglePathPromptDataLoader(dataset=dataset["test"], template=mytemplate, tokenizer=tokenizer,
                                                 tokenizer_wrapper_class=WrapperClass, max_seq_length=args.max_seq_lens,
                                                 batch_size=16, shuffle=False)

    # Build train model and verbalizer
    verbalizer_list = [SoftVerbalizer(tokenizer, model=plm, classes=processor.label_list[i]) for i in range(args.depth)]
    
    prompt_model = TrainHierVerbPromptForClassification(plm=plm, template=mytemplate, verbalizer_list=verbalizer_list, tokenizer=tokenizer,
                                                        freeze_plm=args.freeze_plm, args=args, processor=processor,
                                                        plm_eval_mode=args.plm_eval_mode, use_cuda=use_cuda)
    if use_cuda:
        prompt_model.to(device)

    # Optimizers and Schedulers
    no_decay = ['bias', 'LayerNorm.weight']
    optimizer_grouped_parameters1 = [
        {'params': [p for n, p in prompt_model.plm.named_parameters() if not any(nd in n for nd in no_decay)], 'weight_decay': 0.01},
        {'params': [p for n, p in prompt_model.plm.named_parameters() if any(nd in n for nd in no_decay)], 'weight_decay': 0.0}
    ]
    optimizer1 = AdamW(optimizer_grouped_parameters1, lr=args.lr)

    optimizer_grouped_parameters2 = [
        {'params': prompt_model.verbalizer.group_parameters_1, "lr": args.lr},
        {'params': prompt_model.verbalizer.group_parameters_2, "lr": args.lr2},
    ]
    optimizer2 = AdamW(optimizer_grouped_parameters2)

    tot_step = len(train_dataloader) // args.gradient_accumulation_steps * args.max_epochs
    scheduler1 = get_linear_schedule_with_warmup(optimizer1, num_warmup_steps=0, num_training_steps=tot_step) if args.use_scheduler1 else None
    scheduler2 = get_linear_schedule_with_warmup(optimizer2, num_warmup_steps=0, num_training_steps=tot_step) if args.use_scheduler2 else None

    # Training loop variables
    best_score_macro = 0
    best_score_micro = 0
    best_topk_macro = -1.0
    best_topk_micro = -1.0
    early_stop_count = 0

    current_time = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
    this_run_unicode = f"{current_time}-lr-{args.lr}-lm_training-{args.lm_training}-lm_alpha-{args.lm_alpha}-batch_size-{args.batch_size}"
    writer = SummaryWriter(log_dir=f"runs/combined/{this_run_unicode}")

    # Training loop
    for epoch in range(args.max_epochs):
        print_info(f"------------ Epoch {epoch + 1} ------------")

        if early_stop_count >= args.early_stop:
            print_info("Early stopping triggered.")
            break

        # Standard training step
        prompt_model.train()
        total_loss = 0
        for batch in tqdm(train_dataloader, desc=f"Epoch {epoch+1} Training"):
            loss = prompt_model(batch)
            loss.backward()
            total_loss += loss.item()
            
            optimizer1.step()
            optimizer1.zero_grad()
            optimizer2.step()
            optimizer2.zero_grad()
            if scheduler1:
                scheduler1.step()
            if scheduler2:
                scheduler2.step()
        
        writer.add_scalar('Loss/train', total_loss / len(train_dataloader), epoch)

        # Standard validation
        val_scores = prompt_model.evaluate(validation_dataloader, processor, desc="Validation")
        writer.add_scalar('Val/macro_f1', val_scores['macro_f1'], epoch)
        writer.add_scalar('Val/micro_f1', val_scores['micro_f1'], epoch)

        # Save best model based on validation score
        if val_scores['macro_f1'] > best_score_macro:
            best_score_macro = val_scores['macro_f1']
            best_score_micro = val_scores['micro_f1']
            torch.save(prompt_model.state_dict(), f"ckpts/{this_run_unicode}-best_val.ckpt")
            print_info(f"New best validation score: macro_f1={best_score_macro}, micro_f1={best_score_micro}. Model saved.")
            early_stop_count = 0
        else:
            early_stop_count += 1

        # Combined evaluation phase
        if epoch + 1 > args.pretrain_epochs:
            print_info(f"--- Combined Evaluation at Epoch {epoch + 1} ---")
            model_checkpoint_path = f"ckpts/{this_run_unicode}-best_val.ckpt"
            
            # Step 1: Run embedding logic
            embedding_path = run_embedding_logic(args, model_checkpoint_path, processor, test_dataloader)

            # Step 2: Run topk logic
            topk_scores = run_topk_logic(args, model_checkpoint_path, processor, test_dataloader, embedding_path)

            if topk_scores:
                writer.add_scalar('TopK/macro_f1', topk_scores['macro_f1'], epoch)
                writer.add_scalar('TopK/micro_f1', topk_scores['micro_f1'], epoch)

                # Step 3: Early stopping based on topk scores
                if best_topk_macro < 0: # First time
                    best_topk_macro = topk_scores['macro_f1']
                    best_topk_micro = topk_scores['micro_f1']
                    print_info(f"Initialized best TopK scores: macro_f1={best_topk_macro}, micro_f1={best_topk_micro}")
                else:
                    if topk_scores['macro_f1'] > best_topk_macro:
                        best_topk_macro = topk_scores['macro_f1']
                        best_topk_micro = topk_scores['micro_f1']
                        print_info(f"New best TopK scores: macro_f1={best_topk_macro}, micro_f1={best_topk_micro}. Continuing training.")
                    else:
                        print_info(f"TopK macro_f1 ({topk_scores['macro_f1']}) did not improve from best ({best_topk_macro}). Triggering early stop.")
                        break # Early stop

    writer.close()
    print_info("Training finished.")

if __name__ == "__main__":
    main()
