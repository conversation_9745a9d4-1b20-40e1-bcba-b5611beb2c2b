#!/usr/bin/env python3
"""
测试脚本：验证combined_train.py的修改是否正确
"""

import argparse
import sys
import os

def test_argument_parsing():
    """测试参数解析是否正确"""
    print("Testing argument parsing...")
    
    # 模拟命令行参数
    test_args = [
        '--n', '10',
        '--dataset', 'wos',
        '--shot', '30',
        '--seed', '171',
        '--max_epochs', '20',
        '--topk', '3'
    ]
    
    # 导入combined_train模块
    sys.path.append('.')
    
    try:
        # 创建parser（模拟combined_train.py中的main函数逻辑）
        parser = argparse.ArgumentParser("")
        
        # 添加基本参数
        parser.add_argument("--model", type=str, default='bert')
        parser.add_argument("--model_name_or_path", default='/data/models/models/dienstag/chinese-roberta-wwm-ext')
        parser.add_argument("--result_file", type=str, default="/data/zhousiqi/TACL_physical/result/combined_shot_train.txt")
        parser.add_argument("--multi_label", default=0, type=int)
        parser.add_argument("--multi_verb", default=1, type=int)
        parser.add_argument("--constraint_loss", default=0, type=int)
        parser.add_argument("--constraint_alpha", default=0.9, type=float)
        parser.add_argument("--lm_training", default=1, type=int)
        parser.add_argument("--lm_alpha", default=0.7, type=float)
        parser.add_argument("--lr", default=3e-5, type=float)
        
        # 新增参数n
        parser.add_argument("--n", type=int, default=10, help="Number of epochs to train normally before starting topk evaluation and early stopping")
        
        # 其他必要参数
        parser.add_argument("--dataset", default="wos", type=str)
        parser.add_argument("--shot", type=int, default=30)
        parser.add_argument("--seed", type=int, default=171)
        parser.add_argument("--max_epochs", type=int, default=50)
        parser.add_argument("--topk", type=int, default=3, help="TopK value for retrieval.")
        
        # 解析测试参数
        args = parser.parse_args(test_args)
        
        # 验证参数
        assert args.n == 10, f"Expected n=10, got n={args.n}"
        assert args.dataset == 'wos', f"Expected dataset='wos', got dataset='{args.dataset}'"
        assert args.shot == 30, f"Expected shot=30, got shot={args.shot}"
        assert args.seed == 171, f"Expected seed=171, got seed={args.seed}"
        assert args.max_epochs == 20, f"Expected max_epochs=20, got max_epochs={args.max_epochs}"
        assert args.topk == 3, f"Expected topk=3, got topk={args.topk}"
        
        print("✓ Argument parsing test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Argument parsing test failed: {e}")
        return False

def test_import_statements():
    """测试import语句是否正确"""
    print("Testing import statements...")
    
    try:
        # 测试能否正确导入所需模块
        import time
        import pickle
        from datetime import datetime
        
        print("✓ Import statements test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Import statements test failed: {e}")
        return False

def test_file_syntax():
    """测试文件语法是否正确"""
    print("Testing file syntax...")
    
    try:
        # 尝试编译combined_train.py文件
        with open('combined_train.py', 'r', encoding='utf-8') as f:
            code = f.read()
        
        compile(code, 'combined_train.py', 'exec')
        
        print("✓ File syntax test passed!")
        return True
        
    except SyntaxError as e:
        print(f"✗ File syntax test failed: {e}")
        return False
    except Exception as e:
        print(f"✗ File syntax test failed: {e}")
        return False

def main():
    """运行所有测试"""
    print("Running tests for combined_train.py modifications...")
    print("=" * 50)
    
    tests = [
        test_import_statements,
        test_file_syntax,
        test_argument_parsing,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The modifications look good.")
        return 0
    else:
        print("❌ Some tests failed. Please check the modifications.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
